const drop = document.getElementById('drop');
const pick = document.getElementById('pick');
const files = document.getElementById('files');
const form = document.getElementById('form');
const statusEl = document.getElementById('status');
const result = document.getElementById('result');

function setStatus(msg){ statusEl.textContent = msg || ''; }

;['dragenter','dragover'].forEach(evt=> drop.addEventListener(evt, e=>{ e.preventDefault(); e.stopPropagation(); drop.classList.add('hover'); }));
;['dragleave','drop'].forEach(evt=> drop.addEventListener(evt, e=>{ e.preventDefault(); e.stopPropagation(); drop.classList.remove('hover'); }));
drop.addEventListener('click', ()=> files.click());
pick.addEventListener('click', ()=> files.click());
drop.addEventListener('drop', e=> { if(e.dataTransfer?.files?.length){ files.files = e.dataTransfer.files; } });

form.addEventListener('submit', async (e)=>{
  e.preventDefault();
  if(!files.files.length){ setStatus('no files selected'); return; }
  setStatus('converting… keep the tab open.');
  result.innerHTML = '';

  const fd = new FormData(form);
  Array.from(files.files).forEach(f=> fd.append('files', f));

  try {
    const res = await fetch('/convert', { method:'POST', body: fd });
    if(!res.ok){
      const text = await res.text();
      throw new Error(text || ('http '+res.status));
    }
    const blob = await res.blob();
    const cd = res.headers.get('content-disposition') || '';
    const m = /filename="?([^";]+)"?/i.exec(cd);
    const name = m ? m[1] : 'output.bin';
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url; a.download = name; a.className = 'dl';
    a.textContent = 'download ' + name;
    result.appendChild(a);
    setStatus('done.');
  } catch (err){
    console.error(err);
    setStatus('error: ' + (err.message || err));
  }
});