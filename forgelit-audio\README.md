# forgelit-audio (Flask + FFmpeg)

Audio/video → MP3 converter. Multi-file; returns single MP3 or ZIP. Exposes `/status` for health. Deploy on Render/Railway/Fly (Docker).

## structure
```
forgelit-audio/
  app.py
  templates/
    index.html
  static/
    css/style.css
    js/app.js
  Dockerfile
  requirements.txt
  Procfile
  README.md
```

## env
- `A2M_BITRATE` (default `320k`)
- `PORT` (default `8000`)

## run locally (Docker)
```bash
docker build -t forgelit-audio .
docker run -p 8000:8000 forgelit-audio
# Open http://localhost:8000
# Health: http://localhost:8000/status
```

## run locally (Python)
```bash
# Make sure FFmpeg is installed on your machine
pip install -r requirements.txt
python app.py  # http://localhost:8000
```

## deploy (Render)
1. New → web service → **Docker** → connect repo.
2. Env: Optional `A2M_BITRATE=320k`.
3. Domain: Add `audio.forgelit.com` in "Custom domains" → follow CNAME instructions.
4. Health check: Visit `https://audio.forgelit.com/status` → expect `{ "status": "ok", "ffmpeg": "ffmpeg version ..." }`.

## deploy (Railway/Fly.io)
- Same Dockerfile works. Set port to `8000`. Add custom domain `audio.forgelit.com`.
- Ensure the platform allows long-running requests (FFmpeg). Serverless functions are NOT recommended.

## notes
- Max request size: 200MB (tweak `MAX_CONTENT_LENGTH` in `app.py`).
- Temp files are cleaned after each request.
- Converts audio tracks from video files (drops video stream).

## troubleshooting
- 500 FFmpeg error: Open service logs; most often codec issue. FFmpeg handles almost everything.
- Long jobs timing out: Bump instance class or platform timeouts; avoid serverless.
- If `/status` fails: Container doesn't have FFmpeg (check Dockerfile build logs).