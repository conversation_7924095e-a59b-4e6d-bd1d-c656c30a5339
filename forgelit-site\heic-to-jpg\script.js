// heic -> jpg, fully client-side using heic2any (libheif wasm)
const fileInput = document.getElementById('file-input');
const dropzone = document.getElementById('dropzone');
const browseBtn = document.getElementById('browse-btn');
const results = document.getElementById('results');
const zipAllBtn = document.getElementById('zip-all');
const statusEl = document.getElementById('status');
const qualityInput = document.getElementById('quality');
const qualityVal = document.getElementById('quality-val');

let converted = []; // {name, blob}

function setStatus(msg) {
  statusEl.textContent = msg || '';
}

function humanBytes(n) {
  if (!n && n !== 0) return '';
  const units = ['B','KB','MB','GB'];
  let i = 0;
  while (n >= 1024 && i < units.length-1) { n /= 1024; i++; }
  return n.toFixed(1) + ' ' + units[i];
}

function sanitizeName(name) {
  return name.replace(/\.[^.]+$/, '') // remove extension
             .replace(/[^A-Za-z0-9._-]+/g, '_');
}

async function convertFile(file) {
  const q = parseFloat(qualityInput.value || '0.9');
  try {
    setStatus(`converting ${file.name}…`);
    const output = await heic2any({
      blob: file,
      toType: 'image/jpeg',
      quality: q
    });
    const jpgBlob = Array.isArray(output) ? output[0] : output;
    const base = sanitizeName(file.name);
    const outName = `${base}.jpg`;
    converted.push({ name: outName, blob: jpgBlob });
    appendResult(outName, jpgBlob, file.size, jpgBlob.size);
  } catch (err) {
    console.error('convert error', err);
    appendError(file.name, err);
  } finally {
    setStatus('');
  }
}

function appendResult(name, blob, inSize, outSize) {
  const url = URL.createObjectURL(blob);
  const li = document.createElement('li');
  li.className = 'result-item';
  li.innerHTML = `
    <img class="thumb" alt="${name}" src="${url}" />
    <div class="meta">
      <div class="name">${name}</div>
      <div class="sizes">in: ${humanBytes(inSize)} → out: ${humanBytes(outSize || blob.size)}</div>
      <div class="actions">
        <a class="btn" href="${url}" download="${name}">download</a>
      </div>
    </div>
  `;
  results.appendChild(li);
  zipAllBtn.disabled = converted.length === 0;
}

function appendError(name, err) {
  const li = document.createElement('li');
  li.className = 'result-item error';
  li.innerHTML = `
    <div class="meta">
      <div class="name">${name}</div>
      <div class="sizes">failed: ${(err && err.message) ? err.message : err}</div>
    </div>
  `;
  results.appendChild(li);
}

function handleFiles(files) {
  const arr = Array.from(files).filter(f =>
    /\.(heic|heif)$/i.test(f.name) || /image\/heic|image\/heif/i.test(f.type)
  );
  if (!arr.length) {
    setStatus('no HEIC/HEIF files detected.');
    setTimeout(() => setStatus(''), 1500);
    return;
  }
  arr.forEach(convertFile);
}

browseBtn.addEventListener('click', () => fileInput.click());
fileInput.addEventListener('change', e => handleFiles(e.target.files));

['dragenter','dragover'].forEach(evt => {
  dropzone.addEventListener(evt, e => {
    e.preventDefault();
    e.stopPropagation();
    dropzone.classList.add('hover');
  });
});
['dragleave','drop'].forEach(evt => {
  dropzone.addEventListener(evt, e => {
    e.preventDefault();
    e.stopPropagation();
    dropzone.classList.remove('hover');
  });
});
dropzone.addEventListener('drop', e => {
  if (e.dataTransfer?.files?.length) handleFiles(e.dataTransfer.files);
});
dropzone.addEventListener('click', () => fileInput.click());

qualityInput.addEventListener('input', () => {
  qualityVal.textContent = parseFloat(qualityInput.value).toFixed(2);
});

zipAllBtn.addEventListener('click', async () => {
  if (!converted.length) return;
  const zip = new JSZip();
  const folder = zip.folder('converted') || zip;
  converted.forEach(({name, blob}) => folder.file(name, blob));
  setStatus('zipping…');
  const content = await zip.generateAsync({type: 'blob'});
  saveAs(content, 'heic_to_jpg.zip');
  setStatus('');
});