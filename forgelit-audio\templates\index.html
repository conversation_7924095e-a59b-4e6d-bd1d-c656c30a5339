<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Audio → MP3 (Flask + FFmpeg)</title>
  <link rel="stylesheet" href="/static/css/style.css" />
</head>
<body>
  <header class="site-header">
    <h1>Audio → MP3</h1>
    <p class="sub">Server-side FFmpeg conversion. Drag/drop multiple files; get back 320kbps MP3 (configurable).</p>
  </header>
  <main class="container">
    <form id="form" class="card" action="/convert" method="post" enctype="multipart/form-data">
      <div id="drop" class="drop" tabindex="0">
        <input id="files" type="file" name="files" multiple accept="audio/*,video/*" hidden>
        <p>Drag & drop audio/video files here or <button id="pick" class="btn" type="button">Choose Files</button></p>
        <small>Supported: WAV, M4A, AAC, OGG, FLAC, MP3, OPUS, WEBM, WMA, AIF… (FFmpeg handles most things)</small>
      </div>
      <div class="row">
        <label>Bitrate: <input id="bitrate" name="bitrate" type="text" value="{{ bitrate }}" /></label>
        <button id="convert" class="btn" type="submit">Convert</button>
      </div>
      <div id="status" class="muted"></div>
    </form>
    <div id="result" class="result"></div>
  </main>
  <footer class="site-footer"><p>© 2025 Forgelit • Audio → MP3</p></footer>
  <script src="/static/js/app.js"></script>
</body>
</html>