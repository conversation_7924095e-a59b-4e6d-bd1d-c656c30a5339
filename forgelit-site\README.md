# forgelit-site (static tools)

Minimal static site hosting the client-side tools (HEIC→JPG, EXIF cleaner, bulk resizer, etc). Deploy on Vercel **or** Netlify. No build step.

## structure
```
forgelit-site/
  index.html
  heic-to-jpg/
    index.html
    script.js
  exif-cleaner/
    index.html
    script.js
  bulk-resizer/
    index.html
    script.js
  assets/
    css/
      style.css
      modal.css
    js/
      modal.js
    img/
  vercel.json   # if using vercel
  netlify.toml  # if using netlify (pick one, not both)
```

## deploy (Vercel)
1. Push to GitHub.
2. Vercel → "Import project" → select repo → **framework: other** → build command: empty → output dir: `.`
3. Add domain: `forgelit.com` (and optionally `www.forgelit.com`).
4. DNS: Follow Vercel's records. Set redirect `www → apex`.
5. Done. Changes deploy on every push.

## deploy (Netlify)
1. Push to GitHub.
2. Netlify → "New site from Git" → select repo.
3. Build command: empty; publish dir: `.`
4. Add domain `forgelit.com` → follow DNS/CNAME prompts.
5. Done.

## link to audio app
- Add a link to `https://audio.forgelit.com/` in `index.html` + footers.

## optional config
- `vercel.json` or `netlify.toml` include sane cache headers (immutable for assets, short for HTML).
- Set `window.TF_EMAIL_ENDPOINT` if wiring the privacy modal email capture.

## troubleshooting
- 404s? Ensure file paths are **relative** (e.g., `../assets/...` inside tool folders).
- Stale assets? Cache is long for `js/css`; bump filenames or `?v=2`.