:root { --bg:#0f1115; --fg:#e6e9ef; --muted:#9aa3b2; --card:#171a21; --accent:#8ab4f8; --accent2:#c084fc; --border:#222630; }
*{ box-sizing:border-box } body{ margin:0; font-family:ui-sans-serif,system-ui,Inter,Segoe UI,Roboto,Arial; background:var(--bg); color:var(--fg) }
.container{ max-width:900px; margin:24px auto; padding:0 16px }
.site-header{ padding:16px 22px; border-bottom:1px solid var(--border) }
.site-footer{ padding:16px 22px; border-top:1px solid var(--border); color:var(--muted) }
.sub{ color:var(--muted); margin:4px 0 0 }
.card{ background:var(--card); border:1px solid var(--border); padding:16px; border-radius:12px; margin-top:16px }
.drop{ border:2px dashed var(--border); border-radius:12px; padding:28px; text-align:center }
.drop.hover{ border-color:var(--accent); background:#151925 }
.btn{ background:linear-gradient(90deg, var(--accent), var(--accent2)); color:#111; border:none; padding:8px 12px; border-radius:8px; cursor:pointer; font-weight:700 }
.row{ display:flex; gap:12px; align-items:center; margin-top:12px; flex-wrap:wrap }
input[type="text"]{ background:#0f1115; color:var(--fg); border:1px solid var(--border); padding:8px 10px; border-radius:8px }
.muted{ color:var(--muted); margin-top:8px } .result{ margin-top:16px }
a.dl{ color:var(--accent) }