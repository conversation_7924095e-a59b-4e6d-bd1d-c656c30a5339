const fileInput = document.getElementById('file-input');
const dropzone = document.getElementById('dropzone');
const browseBtn = document.getElementById('browse-btn');
const preset = document.getElementById('preset');
const modeEl = document.getElementById('mode');
const patternEl = document.getElementById('pattern');
const wmTextEl = document.getElementById('wm-text');
const wmSizeEl = document.getElementById('wm-size');
const wmOpEl = document.getElementById('wm-op');
const wmPosEl = document.getElementById('wm-pos');
const widthEl = document.getElementById('width');
const heightEl = document.getElementById('height');
const keepAspect = document.getElementById('keep-aspect');
const formatEl = document.getElementById('format');
const qualityEl = document.getElementById('quality');
const qv = document.getElementById('qv');
const startBtn = document.getElementById('start');
const zipBtn = document.getElementById('zip-all');
const results = document.getElementById('results');
const statusEl = document.getElementById('status');

let images = [];   // File[]
let outputs = [];  // {name, blob}

function setStatus(msg){ statusEl.textContent = msg || ''; }
function sanitizeName(name){ return name.replace(/\.[^.]+$/, '').replace(/[^A-Za-z0-9._-]+/g, '_'); }
function humanBytes(n){ if(n==null) return ''; const u=['B','KB','MB','GB']; let i=0; while(n>=1024&&i<u.length-1){ n/=1024; i++; } return n.toFixed(1)+' '+u[i]; }

browseBtn.addEventListener('click', ()=> fileInput.click());
fileInput.addEventListener('change', e=> addFiles(e.target.files));

['dragenter','dragover'].forEach(evt=> dropzone.addEventListener(evt, e=>{
  e.preventDefault(); e.stopPropagation(); dropzone.classList.add('hover');
}));
['dragleave','drop'].forEach(evt=> dropzone.addEventListener(evt, e=>{
  e.preventDefault(); e.stopPropagation(); dropzone.classList.remove('hover');
}));
dropzone.addEventListener('drop', e=> { if(e.dataTransfer?.files?.length) addFiles(e.dataTransfer.files); });
dropzone.addEventListener('click', ()=> fileInput.click());

preset.addEventListener('change', ()=>{
  // @2x handling
  const v = preset.value;
  if(v === 'none') return;
  if(v.startsWith('long-')){
    const long = parseInt(v.split('-')[1],10);
    widthEl.value = long; heightEl.value = '';
    keepAspect.checked = true;
  } else if(v.startsWith('short-')){
    const short = parseInt(v.split('-')[1],10);
    widthEl.value = ''; heightEl.value = short;
    keepAspect.checked = true;
  } else if(v.includes('@2x')){
    const base = v.replace('@2x','');
    if(base.endsWith('w')){ const w = parseInt(base,10); widthEl.value = w*2; heightEl.value=''; }
    else { const [w,h] = base.split('x').map(n=> parseInt(n,10)); widthEl.value = w*2; heightEl.value = h*2; }
  } else if(v.endsWith('w')){
    widthEl.value = parseInt(v,10);
    heightEl.value = '';
  } else {
    const [w,h] = v.split('x').map(n=> parseInt(n,10));
    widthEl.value = w; heightEl.value = h;
  }
});

qualityEl.addEventListener('input', ()=> qv.textContent = parseFloat(qualityEl.value).toFixed(2));

function addFiles(list){
  const arr = Array.from(list).filter(f=> /^image\//.test(f.type));
  if(!arr.length){ setStatus('no image files detected.'); setTimeout(()=> setStatus(''), 1500); return; }
  images.push(...arr);
  setStatus(`${images.length} file(s) queued.`);
}

function readImage(file){
  return new Promise((resolve,reject)=>{
    const img = new Image();
    img.onload = ()=> resolve(img);
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

async function resizeOne(file){
  // mode logic
  const img = await readImage(file);
  let targetW = parseInt(widthEl.value, 10) || null;
  let targetH = parseInt(heightEl.value, 10) || null;
  const mode = modeEl.value;

  if(!targetW && !targetH) {
    // nothing set: no-op (skip)
    throw new Error('no target size set');
  }

  const srcW = img.naturalWidth, srcH = img.naturalHeight;
  if(mode === 'stretch'){
    // ignore aspect
    if(!targetW) targetW = img.naturalWidth;
    if(!targetH) targetH = img.naturalHeight;
  } else if(mode === 'long'){
    const L = Math.max(img.naturalWidth, img.naturalHeight);
    const s = (targetW || targetH || 0) / L;
    if(!s || s<=0){ throw new Error('set width or height for long edge'); }
    targetW = Math.round(img.naturalWidth * s);
    targetH = Math.round(img.naturalHeight * s);
  } else if(mode === 'short'){
    const S = Math.min(img.naturalWidth, img.naturalHeight);
    const s = (targetW || targetH || 0) / S;
    if(!s || s<=0){ throw new Error('set width or height for short edge'); }
    targetW = Math.round(img.naturalWidth * s);
    targetH = Math.round(img.naturalHeight * s);
  } else if(mode === 'width'){
    if(!targetW){ throw new Error('set width'); }
    targetH = Math.round(img.naturalHeight * (targetW/img.naturalWidth));
  } else if(mode === 'height'){
    if(!targetH){ throw new Error('set height'); }
    targetW = Math.round(img.naturalWidth * (targetH/img.naturalHeight));
  } else if(keepAspect.checked){
    if(targetW && !targetH){
      targetH = Math.round(srcH * (targetW/srcW));
    } else if(targetH && !targetW){
      targetW = Math.round(srcW * (targetH/srcH));
    } else if(targetW && targetH){
      // fit inside bbox
      const sx = targetW/srcW, sy = targetH/srcH;
      const s = Math.min(sx, sy);
      targetW = Math.round(srcW * s);
      targetH = Math.round(srcH * s);
    }
  } else {
    targetW = targetW || srcW;
    targetH = targetH || srcH;
  }

  const canvas = document.createElement('canvas');
  canvas.width = targetW; canvas.height = targetH;
  const ctx = canvas.getContext('2d');
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  ctx.drawImage(img, 0, 0, targetW, targetH);
  // draw watermark
  const wm = (wmTextEl.value || '').trim();
  if (wm) {
    const size = Math.max(6, parseInt(wmSizeEl.value||'32',10));
    const op = Math.min(1, Math.max(0, parseFloat(wmOpEl.value||'0.35')));
    ctx.save();
    ctx.globalAlpha = op; ctx.fillStyle = '#ffffff'; ctx.textBaseline = 'bottom';
    ctx.font = `${size}px Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial`;
    const pad = Math.round(size*0.6);
    const metrics = ctx.measureText(wm);
    let x = pad, y = targetH - pad;
    const pos = wmPosEl.value;
    if (pos === 'br') { x = targetW - metrics.width - pad; y = targetH - pad; }
    else if (pos === 'bl') { x = pad; y = targetH - pad; }
    else if (pos === 'tr') { x = targetW - metrics.width - pad; y = pad + size; }
    else if (pos === 'tl') { x = pad; y = pad + size; }
    else if (pos === 'c') { x = (targetW - metrics.width)/2; y = (targetH + size)/2; }
    ctx.fillText(wm, x, y);
    ctx.restore();
  }

  const fmt = formatEl.value; // jpeg | png | webp
  const mime = fmt === 'png' ? 'image/png' : (fmt === 'webp' ? 'image/webp' : 'image/jpeg');
  const q = parseFloat(qualityEl.value || '0.9');
  const outBlob = await new Promise(res=> canvas.toBlob(b=> res(b), mime, (fmt === 'jpeg' || fmt === 'webp') ? q : undefined));

  const base = sanitizeName(file.name);
  let outName = (patternEl.value || '{base}_{w}x{h}').replace('{base}', base).replace('{w}', targetW).replace('{h}', targetH).replace('{fmt}', (fmt === 'jpeg' ? 'jpg' : fmt));
  outName = outName.replace(/[^A-Za-z0-9._-]+/g, '_');
  outName = outName + '.' + (fmt === 'jpeg' ? 'jpg' : fmt);
  return { name: outName, blob: outBlob, inSize: file.size };
}

function appendResult(out){
  const url = URL.createObjectURL(out.blob);
  const li = document.createElement('li');
  li.className = 'result-item';
  li.innerHTML = `
    <img class="thumb" src="${url}" alt="${out.name}" />
    <div class="meta">
      <div class="name">${out.name}</div>
      <div class="sizes">out: ${humanBytes(out.blob.size)}</div>
      <div class="actions"><a class="btn" href="${url}" download="${out.name}">download</a></div>
    </div>`;
  results.appendChild(li);
}

startBtn.addEventListener('click', async ()=>{
  if(!images.length){ setStatus('add images first'); return; }
  results.innerHTML = ''; outputs = [];
  zipBtn.disabled = true;
  setStatus('resizing…');
  for (const f of images){
    try {
      const out = await resizeOne(f);
      outputs.push(out);
      appendResult(out);
    } catch (e){
      console.error(e);
    }
  }
  setStatus('done.');
  zipBtn.disabled = outputs.length === 0;
});

zipBtn.addEventListener('click', async ()=>{
  if(!outputs.length) return;
  const zip = new JSZip();
  const folder = zip.folder('resized') || zip;
  outputs.forEach(o=> folder.file(o.name, o.blob));
  setStatus('zipping…');
  const blob = await zip.generateAsync({type:'blob'});
  saveAs(blob, 'resized_images.zip');
  setStatus('');
});