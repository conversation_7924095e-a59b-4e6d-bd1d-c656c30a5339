const fileInput = document.getElementById('file-input');
const dropzone = document.getElementById('dropzone');
const browseBtn = document.getElementById('browse-btn');
const results = document.getElementById('results');
const zipAllBtn = document.getElementById('zip-all');
const statusEl = document.getElementById('status');
const qualityInput = document.getElementById('quality');
const qualityVal = document.getElementById('quality-val');

let cleaned = []; // {name, blob}

function setStatus(msg) { statusEl.textContent = msg || ''; }
function humanBytes(n){ if(n==null) return ''; const u=['B','KB','MB','GB'];let i=0;while(n>=1024&&i<u.length-1){n/=1024;i++;}return n.toFixed(1)+' '+u[i]; }
function extOfType(mime){
  if(mime.includes('png')) return 'png';
  if(mime.includes('webp')) return 'webp';
  return 'jpg';
}
function sanitizeName(name){ return name.replace(/\.[^.]+$/, '').replace(/[^A-Za-z0-9._-]+/g,'_'); }

async function readImage(file){
  return new Promise((resolve,reject)=>{
    const img = new Image();
    img.onload = ()=> resolve(img);
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

async function stripExif(file){
  const img = await readImage(file);
  const canvas = document.createElement('canvas');
  canvas.width = img.naturalWidth;
  canvas.height = img.naturalHeight;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(img, 0, 0);
  const type = (file.type && file.type.startsWith('image/')) ? file.type : 'image/jpeg';
  const q = parseFloat(qualityInput.value || '0.9');
  const mime = type.includes('png') ? 'image/png' : (type.includes('webp') ? 'image/webp' : 'image/jpeg');
  return new Promise(res=> canvas.toBlob(b=> res(b), mime, mime==='image/jpeg' ? q : undefined));
}

function metaPreview(meta){
  if(!meta) return 'no exif found';
  const parts = [];
  if(meta.Make || meta.Model) parts.push(`camera: ${meta.Make||''} ${meta.Model||''}`.trim());
  if(meta.DateTimeOriginal) parts.push(`taken: ${new Date(meta.DateTimeOriginal).toISOString().slice(0,19).replace('T',' ')}`);
  if(meta.latitude && meta.longitude) parts.push(`gps: ${meta.latitude.toFixed(5)}, ${meta.longitude.toFixed(5)}`);
  if(meta.LensModel) parts.push(`lens: ${meta.LensModel}`);
  return parts.length ? parts.join(' • ') : 'no notable tags';
}

async function processFile(file){
  try {
    setStatus(`processing ${file.name}…`);
    // parse exif (best-effort; won’t exist for png/webp usually)
    let meta = null;
    try { meta = await exifr.parse(file, { gps: true }); } catch(e){ /* ignore */ }
    const outBlob = await stripExif(file);
    const base = sanitizeName(file.name);
    const ext = extOfType(file.type || '');
    const outName = `${base}.clean.${ext}`;
    cleaned.push({ name: outName, blob: outBlob });
    appendResult(file, outBlob, meta, outName);
  } catch (err){
    console.error('process error', err);
    appendError(file.name, err);
  } finally {
    setStatus('');
  }
}

function appendResult(inFile, outBlob, meta, outName){
  const url = URL.createObjectURL(outBlob);
  const li = document.createElement('li');
  li.className = 'result-item';
  const metaStr = metaPreview(meta);
  li.innerHTML = `
    <img class="thumb" alt="${outName}" src="${url}" />
    <div class="meta">
      <div class="name">${outName}</div>
      <div class="sizes">in: ${humanBytes(inFile.size)} → out: ${humanBytes(outBlob.size)}</div>
      <div class="sizes">${metaStr}</div>
      <div class="actions">
        <a class="btn" href="${url}" download="${outName}">download</a>
      </div>
    </div>
  `;
  results.appendChild(li);
  zipAllBtn.disabled = cleaned.length === 0;
}

function appendError(name, err){
  const li = document.createElement('li'); li.className='result-item error';
  li.innerHTML = `
    <div class="meta">
      <div class="name">${name}</div>
      <div class="sizes">failed: ${(err && err.message) ? err.message : err}</div>
    </div>`;
  results.appendChild(li);
}

function handleFiles(files){
  const arr = Array.from(files).filter(f=> /^image\//.test(f.type));
  if(!arr.length){ setStatus('no image files detected.'); setTimeout(()=>setStatus(''),1500); return; }
  arr.forEach(processFile);
}

browseBtn.addEventListener('click', ()=> fileInput.click());
fileInput.addEventListener('change', e=> handleFiles(e.target.files));

['dragenter','dragover'].forEach(evt=> dropzone.addEventListener(evt, e=>{
  e.preventDefault(); e.stopPropagation(); dropzone.classList.add('hover');
}));
['dragleave','drop'].forEach(evt=> dropzone.addEventListener(evt, e=>{
  e.preventDefault(); e.stopPropagation(); dropzone.classList.remove('hover');
}));
dropzone.addEventListener('drop', e=> { if(e.dataTransfer?.files?.length) handleFiles(e.dataTransfer.files); });
dropzone.addEventListener('click', ()=> fileInput.click());

qualityInput.addEventListener('input', ()=> qualityVal.textContent = parseFloat(qualityInput.value).toFixed(2));

// zip-all (lazy import to keep this page snappy)
let jszipLoaded = false;
async function ensureJSZip(){
  if(jszipLoaded) return;
  await new Promise((resolve,reject)=>{
    const s = document.createElement('script');
    s.src = 'https://cdn.jsdelivr.net/npm/jszip@3.10.1/dist/jszip.min.js';
    s.onload = ()=> resolve(jszipLoaded = true);
    s.onerror = reject;
    document.head.appendChild(s);
  });
  await new Promise((resolve,reject)=>{
    const s = document.createElement('script');
    s.src = 'https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js';
    s.onload = resolve; s.onerror = reject;
    document.head.appendChild(s);
  });
}

zipAllBtn.addEventListener('click', async ()=>{
  if(!cleaned.length) return;
  await ensureJSZip();
  const zip = new JSZip();
  const folder = zip.folder('cleaned') || zip;
  cleaned.forEach(({name, blob})=> folder.file(name, blob));
  setStatus('zipping…');
  const content = await zip.generateAsync({type:'blob'});
  saveAs(content, 'exif_cleaned.zip');
  setStatus('');
});