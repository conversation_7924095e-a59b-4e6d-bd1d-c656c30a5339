:root {
  --bg: #0f1115; --fg: #e6e9ef; --muted: #9aa3b2;
  --card: #171a21; --accent: #8ab4f8; --accent-2: #c084fc;
  --border: #222630;
}

* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, "Helvetica Neue", Arial;
  background: var(--bg);
  color: var(--fg);
}

a { 
  color: var(--accent);
  text-decoration: none; 
}

header.site-header, footer.site-footer {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  align-items: center;
  padding: 16px 22px;
  border-bottom: 1px solid var(--border);
}

footer.site-footer {
  border-top: 1px solid var(--border);
  border-bottom: none; 
}

.brand {
  font-weight: 700;
  color: var(--fg);
}

.container {
  min-height: calc(100vh - 200px);
  max-width: 880px;
  margin: 30px auto;
  padding: 0 16px;
}

h1 {
  margin: 0 0 8px;
  font-size: 2rem;
}

.sub {
  color: var(--muted);
  margin-top: 0;
}

.dropzone {
  border: 2px dashed var(--border);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  background: var(--card);
  margin: 18px 0;
}

.dropzone.hover {
  border-color: var(--accent);
  background: #151925;
}

.btn {
  background: linear-gradient(90deg, var(--accent), var(--accent-2));
  color: #111;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 700;
}

.btn[disabled] {
  opacity: .5;
  cursor: not-allowed;
}

.controls {
  display: flex;
  gap: 16px;
  align-items: center;
  margin: 12px 0 20px;
  flex-wrap: wrap; 
}

.status {
  color: var(--muted);
}

.results {
  list-style: none;
  padding: 0;
  margin: 0; 
  display: grid; 
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 12px;
}

.result-item {
  display: grid;
  grid-template-columns: 96px 1fr;
  gap: 10px;
  padding: 12px;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 10px;
}

.result-item .thumb {
  width: 96px;
  height: 96px;
  object-fit: cover;
  border-radius: 8px;
  background: #0b0d11;
}

.result-item .meta .name {
  font-weight: 600; 
}

.result-item .meta .sizes {
  color: var(--muted);
  font-size: 0.9rem;
  margin: 4px 0 8px;
}

.result-item.error {
  outline: 1px solid #c0392b;
}

.privacy {
  max-width: 85%;
  margin-inline: auto;
  margin-top: 28px;
  color: var(--muted);
  border: 1px solid var(--border);
  border-radius: 24px;
  padding: 12px;
  text-align: center;
}