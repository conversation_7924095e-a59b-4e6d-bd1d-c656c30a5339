import os
import tempfile
import subprocess
import io
import zipfile
import shutil
from pathlib import Path
from flask import Flask, request, send_file, render_template, abort
from werkzeug.utils import secure_filename

# config
MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # raise cap, flask will reject above this
BITRATE = os.getenv('A2M_BITRATE', '320k')
# note: render free tier still enforces platform-side limits; this only guards flask

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

def has_ffmpeg():
    try:
        subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        return True
    except FileNotFoundError:
        return False

@app.route('/status')
def status():
    try:
        out = subprocess.check_output(['ffmpeg', '-version']).decode('utf-8', errors='ignore').split('\\n', 1)[0]
        return {'status': 'ok', 'ffmpeg': out}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}, 500

@app.route('/', methods=['GET'])
def index():
    return render_template('index.html', bitrate=BITRATE, ffmpeg_ok=has_ffmpeg())

def convert_streaming(file_storage, out_dir: Path, bitrate: str) -> Path:
    """stream upload → ffmpeg stdin → mp3 file; no temp input file."""
    # derive a base name for output (safe)
    base = Path(secure_filename(file_storage.filename or 'input')).stem or 'input'
    out_path = out_dir / "{base}.mp3"

    # build ffmpeg command that reads from stdin
    # -i -  : input from stdin
    # -vn   : drop video if present
    # -ar/-ac/-b:a : output audio settings
    cmd = [
        'ffmpeg', '-hide_banner', '-loglevel', 'error',
        '-y', '-i', 'pipe:0',
        '-vn', '-ar', '44100', '-ac', '2', '-b:a', bitrate,
        str(out_path)
    ]
    proc = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    try:
        # stream the uploaded file in chunks into ffmpeg stdin
        chunk = file_storage.stream.read(1024 * 512)  # 512KB chunks
        total = 0
        while chunk:
            proc.stdin.write(chunk)
            total += len(chunk)
            chunk = file_storage.stream.read(1024 * 512)
        proc.stdin.close()
        rc = proc.wait()
        if rc != 0 or not out_path.exists() or out_path.stat().st_size == 0:
            err = (proc.stderr.read() or b'conversion failed').decode('utf-8', errors='ignore')
            raise RuntimeError(err.strip()[:4000])
        return out_path
    finally:
        # ensure pipes are closed
        try:
            if proc.stdin and not proc.stdin.closed:
                proc.stdin.close()
        except Exception:
            pass

@app.route('/convert', methods=['POST'])
def convert():
    if not has_ffmpeg():
        return abort(500, description='ffmpeg not available in environment')
    files = request.files.getlist('files')
    bitrate = request.form.get('bitrate', BITRATE)
    if not files:
        return abort(400, description='no files uploaded')

    tmp_root = Path(tempfile.mkdtemp(prefix='a2m_'))
    out_dir = tmp_root / 'out'
    out_dir.mkdir(parents=True, exist_ok=True)

    out_paths = []
    try:
        for fs in files:
            if not fs or not fs.filename:
                continue
            try:
                out_paths.append(convert_streaming(fs, out_dir, bitrate))
            except Exception as e:
                # if one fails, continue others; collect at least one success
                print('stream convert error:', e)

        if not out_paths:
            return abort(400, description='no valid files to convert')

        if len(out_paths) == 1:
            fp = out_paths[0]
            return send_file(fp, as_attachment=True, download_name=fp.name, mimetype='audio/mpeg')
        else:
            mem = io.BytesIO()
            with zipfile.ZipFile(mem, 'w', compression=zipfile.ZIP_DEFLATED) as z:
                for p in out_paths:
                    z.write(p, arcname=p.name)
            mem.seek(0)
            return send_file(mem, as_attachment=True, download_name='converted_mp3.zip', mimetype='application/zip')
    finally:
        try:
            shutil.rmtree(tmp_root, ignore_errors=True)
        except Exception:
            pass

@app.errorhandler(413)
def too_large(e):
    return ('file too large for server limits', 413)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.getenv('PORT', 8000)))