/* modal */
.modal-backdrop {
  position: fixed; inset: 0; background: rgba(0,0,0,.6);
  display: none; align-items: center; justify-content: center; z-index: 9999;
}
.modal-backdrop.show { display: flex; }
.modal {
  width: min(560px, 92vw); background: #171a21; color: #e6e9ef;
  border: 1px solid #222630; border-radius: 14px; padding: 18px;
  box-shadow: 0 20px 60px rgba(0,0,0,.45);
}
.modal header { display:flex; justify-content: space-between; align-items:center; margin-bottom: 8px; }
.modal h3 { margin: 0; font-size: 1.25rem; }
.modal .close { background: transparent; border: 0; color: #e6e9ef; font-size: 20px; cursor: pointer; }
.modal .body { color: #9aa3b2; line-height: 1.5; }
.modal form { display:flex; gap: 8px; margin-top: 12px; }
.modal input[type="email"] {
  flex:1; padding: 10px 12px; border-radius: 10px; border:1px solid #222630; background:#0f1115; color:#e6e9ef;
}
.modal .btn.primary { background: linear-gradient(90deg, #8ab4f8, #c084fc); color:#111; border:0; padding:10px 14px; border-radius:10px; font-weight:700; cursor:pointer; }
.modal .fine { font-size:.85rem; color:#9aa3b2; margin-top: 8px; }
.modal .ok-row { display:flex; gap:10px; align-items:center; margin-top: 10px; }
.modal .ok-row input { width:18px; height:18px; }
.privacy-link { color:#9aa3b2; text-decoration: underline; cursor: pointer; }